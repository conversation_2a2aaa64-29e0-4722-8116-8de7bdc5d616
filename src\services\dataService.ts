/**
 * Zenith Pulse Manager - Data Service Layer
 * طبقة خدمات البيانات لتطبيق Zenith Pulse Manager
 * 
 * Comprehensive data service layer with caching and offline-first functionality
 * Based on 2024-2025 best practices for modern web applications
 */

import { db, Task, Project, Note, AnalyticsData, UserSettings } from '@/lib/database';
import { v4 as uuidv4 } from 'uuid';

// ===== CACHE MANAGEMENT =====

class DataCache {
  private cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set(key: string, data: unknown, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get<T>(key: string): T | undefined { // Explicitly define generic T and return type
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    return entry.data as T; // Explicitly cast
  }

  invalidate(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }
}

const cache = new DataCache();

// ===== TASK SERVICE =====

export class TaskService {
  static async getAllTasks(): Promise<Task[]> {
    const cacheKey = 'tasks:all';
    const cached = cache.get<Task[]>(cacheKey);
    if (cached !== undefined) return cached;

    const tasks = await db.tasks.orderBy('createdAt').reverse().toArray();
    cache.set(cacheKey, tasks);
    return tasks;
  }

  static async getTaskById(id: string): Promise<Task | undefined> {
    const cacheKey = `task:${id}`;
    const cached = cache.get<Task>(cacheKey);
    if (cached !== undefined) return cached;

    const task = await db.tasks.get(id);
    if (task) cache.set(cacheKey, task);
    return task;
  }

  static async getTasksByProject(projectId: string): Promise<Task[]> {
    const cacheKey = `tasks:project:${projectId}`;
    const cached = cache.get<Task[]>(cacheKey);
    if (cached !== undefined) return cached;

    const tasks = await db.tasks.where('projectId').equals(projectId).toArray();
    cache.set(cacheKey, tasks);
    return tasks;
  }

  static async getTasksByStatus(status: Task['status']): Promise<Task[]> {
    const cacheKey = `tasks:status:${status}`;
    const cached = cache.get<Task[]>(cacheKey);
    if (cached !== undefined) return cached;

    const tasks = await db.tasks.where('status').equals(status).toArray();
    cache.set(cacheKey, tasks);
    return tasks;
  }

  static async getTasksByPriority(priority: Task['priority']): Promise<Task[]> {
    const cacheKey = `tasks:priority:${priority}`;
    const cached = cache.get<Task[]>(cacheKey);
    if (cached !== undefined) return cached;

    const tasks = await db.tasks.where('priority').equals(priority).toArray();
    cache.set(cacheKey, tasks);
    return tasks;
  }

  static async getTodayTasks(): Promise<Task[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day
    const cacheKey = `tasks:today:${today.toISOString().split('T')[0]}`; // Fixed cache key
    const cached = cache.get<Task[]>(cacheKey);
    if (cached !== undefined) return cached;

    const tasks = await db.tasks.filter(task => 
      task.dueDate && 
      task.dueDate.toISOString().split('T')[0] === today.toISOString().split('T')[0]
    ).toArray();
    cache.set(cacheKey, tasks, 60 * 60 * 1000); // 1 hour TTL
    return tasks;
  }

  static async createTask(taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    const task: Task = {
      ...taskData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.tasks.add(task);
    cache.invalidate('tasks');
    
    // Update analytics
    await this.updateTaskAnalytics('created');
    
    return task;
  }

  static async updateTask(id: string, updates: Partial<Task>): Promise<Task | undefined> {
    const existingTask = await db.tasks.get(id);
    if (!existingTask) return undefined;

    const wasCompleted = existingTask.status === 'completed';
    const isNowCompleted = updates.status === 'completed';

    await db.tasks.update(id, {
      ...updates,
      updatedAt: new Date(),
      ...(isNowCompleted && !wasCompleted ? { completedAt: new Date() } : {})
    });

    const updatedTask = await db.tasks.get(id);
    cache.invalidate('tasks');
    cache.invalidate(`task:${id}`);

    // Update analytics if task was completed
    if (isNowCompleted && !wasCompleted) {
      await this.updateTaskAnalytics('completed');
    }

    return updatedTask;
  }

  static async deleteTask(id: string): Promise<boolean> {
    const result = await db.tasks.delete(id);
    cache.invalidate('tasks');
    cache.invalidate(`task:${id}`);
    return result === 1;
  }

  static async getTaskStats(): Promise<{
    total: number;
    completed: number;
    inProgress: number;
    todo: number;
    overdue: number;
  }> {
    const cacheKey = 'tasks:stats';
    const cached = cache.get<{
      total: number;
      completed: number;
      inProgress: number;
      todo: number;
      overdue: number;
    }>(cacheKey);
    if (cached !== undefined) return cached;

    const tasks = await this.getAllTasks();
    const today = new Date();
    
    const stats = {
      total: tasks.length,
      completed: tasks.filter(t => t.status === 'completed').length,
      inProgress: tasks.filter(t => t.status === 'inProgress').length,
      todo: tasks.filter(t => t.status === 'todo').length,
      overdue: tasks.filter(t => 
        t.dueDate && 
        t.dueDate < today && 
        t.status !== 'completed'
      ).length
    };

    cache.set(cacheKey, stats, 60 * 1000); // 1 minute TTL
    return stats;
  }

  private static async updateTaskAnalytics(action: 'created' | 'completed'): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let analytics = await db.analytics.where('date').equals(today).first();

    if (!analytics) {
      analytics = {
        id: uuidv4(),
        date: today,
        tasksCompleted: 0,
        tasksCreated: 0,
        projectsActive: 0,
        projectsCompleted: 0,
        notesCreated: 0,
        focusTimeMinutes: 0,
        productivityScore: 0,
        createdAt: new Date()
      };
    }

    if (action === 'created') {
      analytics.tasksCreated += 1;
    } else if (action === 'completed') {
      analytics.tasksCompleted += 1;
    }

    // Calculate productivity score
    analytics.productivityScore = Math.min(100, 
      (analytics.tasksCompleted * 10) + 
      (analytics.projectsCompleted * 20) + 
      (analytics.focusTimeMinutes / 60 * 5)
    );

    await db.analytics.put(analytics);
    cache.invalidate('analytics');
  }
}

// ===== PROJECT SERVICE =====

export class ProjectService {
  static async getAllProjects(): Promise<Project[]> {
    const cacheKey = 'projects:all';
    const cached = cache.get<Project[]>(cacheKey);
    if (cached !== undefined) return cached;

    const projects = await db.projects.orderBy('createdAt').reverse().toArray();
    cache.set(cacheKey, projects);
    return projects;
  }

  static async getProjectById(id: string): Promise<Project | undefined> {
    const cacheKey = `project:${id}`;
    const cached = cache.get<Project>(cacheKey);
    if (cached !== undefined) return cached;

    const project = await db.projects.get(id);
    if (project) cache.set(cacheKey, project);
    return project;
  }

  static async getProjectsByStatus(status: Project['status']): Promise<Project[]> {
    const cacheKey = `projects:status:${status}`;
    const cached = cache.get<Project[]>(cacheKey);
    if (cached !== undefined) return cached;

    const projects = await db.projects.where('status').equals(status).toArray();
    cache.set(cacheKey, projects);
    return projects;
  }

  static async createProject(projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'tasks' | 'team'>): Promise<Project> {
    const project: Project = {
      ...projectData,
      id: uuidv4(),
      tasks: [],
      team: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.projects.add(project);
    cache.invalidate('projects');
    return project;
  }

  static async updateProject(id: string, updates: Partial<Project>): Promise<Project | undefined> {
    const existingProject = await db.projects.get(id);
    if (!existingProject) return undefined;

    const wasCompleted = existingProject.status === 'completed';
    const isNowCompleted = updates.status === 'completed';

    await db.projects.update(id, {
      ...updates,
      updatedAt: new Date(),
      ...(isNowCompleted && !wasCompleted ? { completedAt: new Date() } : {})
    });

    const updatedProject = await db.projects.get(id);
    cache.invalidate('projects');
    cache.invalidate(`project:${id}`);

    // Update analytics if project was completed
    if (isNowCompleted && !wasCompleted) {
      await this.updateProjectAnalytics('completed');
    }

    return updatedProject;
  }

  static async deleteProject(id: string): Promise<boolean> {
    // Also delete associated tasks
    await db.tasks.where('projectId').equals(id).delete();
    
    const result = await db.projects.delete(id);
    cache.invalidate('projects');
    cache.invalidate(`project:${id}`);
    cache.invalidate('tasks');
    return result === 1;
  }

  static async addTaskToProject(projectId: string, taskId: string): Promise<boolean> {
    const project = await db.projects.get(projectId);
    if (!project) return false;

    if (!project.tasks.includes(taskId)) {
      project.tasks.push(taskId);
      await db.projects.update(projectId, { tasks: project.tasks });
      cache.invalidate(`project:${projectId}`);
      cache.invalidate('projects');
    }

    return true;
  }

  static async removeTaskFromProject(projectId: string, taskId: string): Promise<boolean> {
    const project = await db.projects.get(projectId);
    if (!project) return false;

    project.tasks = project.tasks.filter(id => id !== taskId);
    await db.projects.update(projectId, { tasks: project.tasks });
    cache.invalidate(`project:${projectId}`);
    cache.invalidate('projects');
    return true;
  }

  static async getProjectStats(): Promise<{
    total: number;
    active: number;
    completed: number;
    pending: number;
    averageProgress: number;
  }> {
    const cacheKey = 'projects:stats';
    const cached = cache.get<{
      total: number;
      active: number;
      completed: number;
      pending: number;
      averageProgress: number;
    }>(cacheKey);
    if (cached !== undefined) return cached;

    const projects = await this.getAllProjects();
    
    const stats = {
      total: projects.length,
      active: projects.filter(p => p.status === 'active').length,
      completed: projects.filter(p => p.status === 'completed').length,
      pending: projects.filter(p => p.status === 'pending').length,
      averageProgress: projects.length > 0 
        ? Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length)
        : 0
    };

    cache.set(cacheKey, stats, 60 * 1000); // 1 minute TTL
    return stats;
  }

  private static async updateProjectAnalytics(action: 'completed'): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let analytics = await db.analytics.where('date').equals(today).first();

    if (!analytics) {
      analytics = {
        id: uuidv4(),
        date: today,
        tasksCompleted: 0,
        tasksCreated: 0,
        projectsActive: 0,
        projectsCompleted: 0,
        notesCreated: 0,
        focusTimeMinutes: 0,
        productivityScore: 0,
        createdAt: new Date()
      };
    }

    if (action === 'completed') {
      analytics.projectsCompleted += 1;
    }

    // Recalculate productivity score
    analytics.productivityScore = Math.min(100, 
      (analytics.tasksCompleted * 10) + 
      (analytics.projectsCompleted * 20) + 
      (analytics.focusTimeMinutes / 60 * 5)
    );

    await db.analytics.put(analytics);
    cache.invalidate('analytics');
  }
}

// ===== NOTE SERVICE =====

export class NoteService {
  static async getAllNotes(): Promise<Note[]> {
    const cacheKey = 'notes:all';
    const cached = cache.get(cacheKey) as Note[] | null;
    if (cached) return cached;

    const notes = await db.notes.orderBy('updatedAt').reverse().toArray();
    cache.set(cacheKey, notes);
    return notes;
  }

  static async getNoteById(id: string): Promise<Note | undefined> {
    const cacheKey = `note:${id}`;
    const cached = cache.get(cacheKey) as Note | null;
    if (cached) return cached;

    const note = await db.notes.get(id);
    if (note) cache.set(cacheKey, note);
    return note;
  }

  static async getPinnedNotes(): Promise<Note[]> {
    const cacheKey = 'notes:pinned';
    const cached = cache.get<Note[]>(cacheKey);
    if (cached !== undefined) return cached;

    const notes = await db.notes.where('isPinned').equals(true).toArray();
    cache.set(cacheKey, notes);
    return notes;
  }

  static async createNote(noteData: Omit<Note, 'id' | 'createdAt' | 'updatedAt' | 'lastModified' | 'wordCount'>): Promise<Note> {
    const note: Note = {
      ...noteData,
      id: uuidv4(),
      wordCount: noteData.content ? noteData.content.split(/\s+/).length : 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastModified: new Date(),
      isPinned: noteData.isPinned || false,
      isArchived: noteData.isArchived || false
    };

    await db.notes.add(note);
    cache.invalidate('notes');

    // Update analytics
    await this.updateNoteAnalytics('created');

    return note;
  }

  static async updateNote(id: string, updates: Partial<Note>): Promise<Note | undefined> {
    const updatedData = {
      ...updates,
      updatedAt: new Date(),
      lastModified: new Date()
    };

    if (updates.content) {
      updatedData.wordCount = updates.content.split(/\s+/).length;
    }

    await db.notes.update(id, updatedData);
    const updatedNote = await db.notes.get(id);

    cache.invalidate('notes');
    cache.invalidate(`note:${id}`);

    return updatedNote;
  }

  static async deleteNote(id: string): Promise<boolean> {
    const result = await db.notes.delete(id);
    cache.invalidate('notes');
    cache.invalidate(`note:${id}`);
    return result === 1;
  }

  static async togglePin(id: string): Promise<boolean> {
    const note = await db.notes.get(id);
    if (!note) return false;

    await db.notes.update(id, { isPinned: !note.isPinned });
    cache.invalidate('notes');
    cache.invalidate(`note:${id}`);
    return true;
  }

  static async archiveNote(id: string): Promise<boolean> {
    const result = await db.notes.update(id, { isArchived: true });
    cache.invalidate('notes');
    cache.invalidate(`note:${id}`);
    return result === 1;
  }

  static async getNoteStats(): Promise<{
    total: number;
    pinned: number;
    archived: number;
    totalWords: number;
  }> {
    const cacheKey = 'notes:stats';
    const cached: any = cache.get<{
      total: number;
      pinned: number;
      archived: number;
      totalWords: number;
    }>(cacheKey);
    if (cached !== undefined) return cached as {
      total: number;
      pinned: number;
      archived: number;
      totalWords: number;
    };

    const notes = await this.getAllNotes();

    const stats = {
      total: notes.length,
      pinned: notes.filter(n => n.isPinned).length,
      archived: notes.filter(n => n.isArchived).length,
      totalWords: notes.reduce((sum, n) => sum + n.wordCount, 0)
    };

    cache.set(cacheKey, stats, 60 * 1000); // 1 minute TTL
    return stats;
  }

  private static async updateNoteAnalytics(action: 'created'): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let analytics = await db.analytics.where('date').equals(today).first();

    if (!analytics) {
      analytics = {
        id: uuidv4(),
        date: today,
        tasksCompleted: 0,
        tasksCreated: 0,
        projectsActive: 0,
        projectsCompleted: 0,
        notesCreated: 0,
        focusTimeMinutes: 0,
        productivityScore: 0,
        createdAt: new Date()
      };
    }

    if (action === 'created') {
      analytics.notesCreated += 1;
    }

    await db.analytics.put(analytics);
    cache.invalidate('analytics');
  }
}

// ===== ANALYTICS SERVICE =====

export class AnalyticsService {
  static async getTodayAnalytics(): Promise<AnalyticsData | null> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const cacheKey = `analytics:${today.toISOString().split('T')[0]}`;
    const cached: any = cache.get<AnalyticsData>(cacheKey);
    if (cached !== undefined) return cached as AnalyticsData;

    const analytics = await db.analytics.where('date').equals(today).first();
    if (analytics) cache.set(cacheKey, analytics, 60 * 1000); // 1 minute TTL
    return analytics || null;
  }

  static async getAnalyticsRange(startDate: Date, endDate: Date): Promise<AnalyticsData[]> {
    const startOfDay = (date: Date) => {
      const d = new Date(date);
      d.setHours(0, 0, 0, 0);
      return d;
    };
    const endOfDay = (date: Date) => {
      const d = new Date(date);
      d.setHours(23, 59, 59, 999);
      return d;
    };

    const sDate = startOfDay(startDate);
    const eDate = endOfDay(endDate);

    const cacheKey = `analytics:${sDate.toISOString().split('T')[0]}:${eDate.toISOString().split('T')[0]}`;
    const cached: any = cache.get<AnalyticsData[]>(cacheKey);
    if (cached !== undefined) return cached as AnalyticsData[];

    const analytics = await db.analytics
      .where('date')
      .between(sDate, eDate, true, true)
      .toArray();

    cache.set(cacheKey, analytics, 5 * 60 * 1000); // 5 minutes TTL
    return analytics;
  }

  static async getWeeklyAnalytics(): Promise<AnalyticsData[]> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 7);

    return this.getAnalyticsRange(startDate, endDate);
  }

  static async getMonthlyAnalytics(): Promise<AnalyticsData[]> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - 1);

    return this.getAnalyticsRange(startDate, endDate);
  }

  static async updateFocusTime(minutes: number): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let analytics = await db.analytics.where('date').equals(today).first();

    if (!analytics) {
      analytics = {
        id: uuidv4(),
        date: today,
        tasksCompleted: 0,
        tasksCreated: 0,
        projectsActive: 0,
        projectsCompleted: 0,
        notesCreated: 0,
        focusTimeMinutes: 0,
        productivityScore: 0,
        createdAt: new Date()
      };
    }

    analytics.focusTimeMinutes += minutes;

    // Recalculate productivity score
    analytics.productivityScore = Math.min(100,
      (analytics.tasksCompleted * 10) +
      (analytics.projectsCompleted * 20) +
      (analytics.focusTimeMinutes / 60 * 5)
    );

    await db.analytics.put(analytics);
    cache.invalidate('analytics');
  }
}

// ===== SEARCH SERVICE =====

export class SearchService {
  static async search(query: string, type?: 'task' | 'project' | 'note'): Promise<{
    tasks: Task[];
    projects: Project[];
    notes: Note[];
  }> {
    const searchQuery = query.toLowerCase().trim();
    if (!searchQuery) {
      return { tasks: [], projects: [], notes: [] };
    }

    const cacheKey = `search:${searchQuery}:${type || 'all'}`;
    const cached: any = cache.get<{
      tasks: Task[];
      projects: Project[];
      notes: Note[];
    }>(cacheKey);
    if (cached !== undefined) return cached as {
      tasks: Task[];
      projects: Project[];
      notes: Note[];
    };

    let searchResults = await db.searchIndex
      .where('searchText')
      .startsWithIgnoreCase(searchQuery)
      .or('searchText')
      .anyOfIgnoreCase(searchQuery.split(' '))
      .toArray();

    if (type) {
      searchResults = searchResults.filter(result => result.type === type);
    }

    const taskIds = searchResults.filter(r => r.type === 'task').map(r => r.entityId);
    const projectIds = searchResults.filter(r => r.type === 'project').map(r => r.entityId);
    const noteIds = searchResults.filter(r => r.type === 'note').map(r => r.entityId);

    const [tasks, projects, notes] = await Promise.all([
      taskIds.length > 0 ? db.tasks.where('id').anyOf(taskIds).toArray() : [],
      projectIds.length > 0 ? db.projects.where('id').anyOf(projectIds).toArray() : [],
      noteIds.length > 0 ? db.notes.where('id').anyOf(noteIds).toArray() : []
    ]);

    const result = { tasks, projects, notes };
    cache.set(cacheKey, result, 2 * 60 * 1000); // 2 minutes TTL
    return result;
  }

  static async getRecentSearches(): Promise<string[]> {
    // This would typically be stored in localStorage or a separate table
    const searches = localStorage.getItem('zenith-recent-searches');
    return searches ? JSON.parse(searches) : [];
  }

  static async addRecentSearch(query: string): Promise<void> {
    const recent = await this.getRecentSearches();
    const updated = [query, ...recent.filter(s => s !== query)].slice(0, 10);
    localStorage.setItem('zenith-recent-searches', JSON.stringify(updated));
  }
}

// ===== SETTINGS SERVICE =====

export class SettingsService {
  static async getSettings(): Promise<UserSettings> {
    const cacheKey = 'settings:user';
    const cached: any = cache.get<UserSettings>(cacheKey);
    if (cached !== undefined) return cached as UserSettings;

    let settings = await db.settings.get('user-settings');
    if (!settings) {
      // Create default settings if none exist
      await db.initializeDefaultSettings();
      settings = await db.settings.get('user-settings');
    }

    if (settings) cache.set(cacheKey, settings);
    return settings!;
  }

  static async updateSettings(updates: Partial<UserSettings>): Promise<UserSettings> {
    await db.settings.update('user-settings', {
      ...updates,
      updatedAt: new Date()
    });

    const updatedSettings = await db.settings.get('user-settings');
    cache.invalidate('settings');
    return updatedSettings!;
  }

  static async resetSettings(): Promise<UserSettings> {
    await db.settings.delete('user-settings');
    await db.initializeDefaultSettings();
    const settings = await db.settings.get('user-settings');
    cache.invalidate('settings');
    return settings!;
  }
}

// Export cache for external use
export { cache as dataCache };
